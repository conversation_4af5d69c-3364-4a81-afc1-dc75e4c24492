"""
测试 httpx 改进功能

验证 macOS M1 Chrome User-Agent 设置和 4xx 状态码错误处理功能。
"""

import pytest
import asyncio
import httpx
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from src.backend.agents.web_content_processor import WebScraper
from src.backend.config import NetworkRetryConfig


class TestUserAgentConfiguration:
    """测试 User-Agent 配置"""

    def test_macos_m1_chrome_user_agent_in_webscraper(self):
        """测试 WebScraper 使用正确的 macOS M1 Chrome User-Agent"""
        scraper = WebScraper()
        
        # 验证 User-Agent 是否正确设置
        expected_user_agent = (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36"
        )
        
        actual_user_agent = scraper.client.headers.get("User-Agent")
        assert actual_user_agent == expected_user_agent, f"Expected User-Agent: {expected_user_agent}, got: {actual_user_agent}"

    def test_user_agent_contains_macos_chrome_identifiers(self):
        """测试 User-Agent 包含 macOS 和 Chrome 标识符"""
        scraper = WebScraper()
        user_agent = scraper.client.headers.get("User-Agent")
        
        # 验证包含关键标识符
        assert "Macintosh" in user_agent, "User-Agent should contain 'Macintosh'"
        assert "Intel Mac OS X" in user_agent, "User-Agent should contain 'Intel Mac OS X'"
        assert "Chrome" in user_agent, "User-Agent should contain 'Chrome'"
        assert "Safari" in user_agent, "User-Agent should contain 'Safari'"
        assert "AppleWebKit" in user_agent, "User-Agent should contain 'AppleWebKit'"


class TestHttpx4xxErrorHandling:
    """测试 4xx 状态码错误处理"""

    @pytest.mark.asyncio
    async def test_4xx_error_marked_as_url_access_failure(self):
        """测试 4xx 错误被标记为 URL 访问失败"""
        scraper = WebScraper()
        
        # 模拟 4xx 响应
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.reason_phrase = "Not Found"
        mock_response.text = "Page not found"
        mock_response.content = b"Page not found"
        mock_response.headers = {"Content-Type": "text/html"}
        
        with patch.object(scraper.client, 'get', return_value=mock_response) as mock_get:
            result = await scraper.fetch_url("https://example.com/nonexistent")
            
            # 验证结果
            assert result["success"] is False, "4xx error should be marked as failure"
            assert result["error_type"] == "client_error", "Error type should be 'client_error'"
            assert result["status_code"] == 404, "Status code should be preserved"
            assert "URL access failed" in result["error"], "Error message should indicate URL access failure"
            assert "404" in result["error"], "Error message should contain status code"
            assert "Not Found" in result["error"], "Error message should contain reason phrase"

    @pytest.mark.asyncio
    async def test_different_4xx_status_codes(self):
        """测试不同的 4xx 状态码都被正确处理"""
        scraper = WebScraper()
        
        test_cases = [
            (400, "Bad Request"),
            (401, "Unauthorized"),
            (403, "Forbidden"),
            (404, "Not Found"),
            (429, "Too Many Requests"),
            (499, "Client Closed Request")
        ]
        
        for status_code, reason_phrase in test_cases:
            mock_response = MagicMock()
            mock_response.status_code = status_code
            mock_response.reason_phrase = reason_phrase
            
            with patch.object(scraper.client, 'get', return_value=mock_response):
                result = await scraper.fetch_url(f"https://example.com/test{status_code}")
                
                assert result["success"] is False, f"Status {status_code} should be marked as failure"
                assert result["error_type"] == "client_error", f"Status {status_code} should have error_type 'client_error'"
                assert result["status_code"] == status_code, f"Status code should be {status_code}"
                assert str(status_code) in result["error"], f"Error message should contain {status_code}"

    @pytest.mark.asyncio
    async def test_4xx_errors_not_retried(self):
        """测试 4xx 错误不会被重试"""
        retry_config = NetworkRetryConfig(max_retries=3)
        scraper = WebScraper(retry_config=retry_config)
        
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.reason_phrase = "Not Found"
        
        with patch.object(scraper.client, 'get', return_value=mock_response) as mock_get:
            result = await scraper.fetch_url("https://example.com/nonexistent")
            
            # 验证只调用了一次，没有重试
            assert mock_get.call_count == 1, "4xx errors should not be retried"
            assert result["attempts"] == 1, "Should only have 1 attempt for 4xx errors"

    @pytest.mark.asyncio
    async def test_5xx_errors_still_retried(self):
        """测试 5xx 错误仍然会被重试"""
        retry_config = NetworkRetryConfig(max_retries=2, retry_on_http_error=True, retry_http_status_codes=[500])
        scraper = WebScraper(retry_config=retry_config)
        
        # 模拟 5xx 错误
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.reason_phrase = "Internal Server Error"
        
        mock_exception = httpx.HTTPStatusError("500 Internal Server Error", request=MagicMock(), response=mock_response)
        
        with patch.object(scraper.client, 'get', side_effect=mock_exception) as mock_get:
            result = await scraper.fetch_url("https://example.com/server-error")
            
            # 验证进行了重试（1次初始请求 + 2次重试 = 3次）
            assert mock_get.call_count == 3, "5xx errors should be retried according to config"
            assert result["attempts"] == 3, "Should have 3 attempts for 5xx errors"

    @pytest.mark.asyncio
    async def test_2xx_success_not_affected(self):
        """测试 2xx 成功响应不受影响"""
        scraper = WebScraper()
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.reason_phrase = "OK"
        mock_response.text = "Success content"
        mock_response.content = b"Success content"
        mock_response.headers = {"Content-Type": "text/html"}
        
        with patch.object(scraper.client, 'get', return_value=mock_response):
            result = await scraper.fetch_url("https://example.com/success")
            
            # 验证成功响应
            assert result["success"] is True, "2xx responses should be successful"
            assert result["status_code"] == 200, "Status code should be 200"
            assert result["content"] == "Success content", "Content should be preserved"


class TestLoggingBehavior:
    """测试日志记录行为"""

    @pytest.mark.asyncio
    async def test_4xx_error_logs_warning(self):
        """测试 4xx 错误记录警告日志"""
        scraper = WebScraper()
        
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.reason_phrase = "Not Found"
        
        with patch.object(scraper.client, 'get', return_value=mock_response):
            with patch('src.backend.agents.web_content_processor.logger') as mock_logger:
                await scraper.fetch_url("https://example.com/nonexistent")
                
                # 验证记录了警告日志
                mock_logger.warning.assert_called()
                warning_call = mock_logger.warning.call_args
                assert "URL access failed with 4xx status code" in str(warning_call)
                assert "404" in str(warning_call)

    @pytest.mark.asyncio
    async def test_successful_request_logs_info(self):
        """测试成功请求记录信息日志"""
        scraper = WebScraper()
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.reason_phrase = "OK"
        mock_response.text = "Success content"
        mock_response.content = b"Success content"
        mock_response.headers = {"Content-Type": "text/html"}
        
        with patch.object(scraper.client, 'get', return_value=mock_response):
            with patch('src.backend.agents.web_content_processor.logger') as mock_logger:
                await scraper.fetch_url("https://example.com/success")
                
                # 验证记录了信息日志
                mock_logger.info.assert_called()
                info_calls = [call for call in mock_logger.info.call_args_list if "Web content fetch completed" in str(call)]
                assert len(info_calls) > 0, "Should log successful fetch completion"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
