"""
API数据模型

定义API请求和响应的数据结构。
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class APIResponse(BaseModel):
    """标准API响应格式"""
    success: bool = True
    data: Optional[Any] = None
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = None


class ErrorResponse(BaseModel):
    """错误响应格式"""
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = None


class WorkflowCreateRequest(BaseModel):
    """创建工作流请求"""
    topic: str = Field(..., description="调研主题")
    scope: Optional[str] = Field(None, description="调研范围")
    target_audience: str = Field(default="技术专家", description="目标受众")
    output_format: str = Field(default="技术报告", description="输出格式")
    research_objectives: Optional[List[str]] = Field(default=[], description="调研目标")
    priority: int = Field(default=1, description="优先级")
    metadata: Optional[Dict[str, Any]] = Field(default={}, description="元数据")


class WorkflowResponse(BaseModel):
    """工作流响应"""
    workflow_id: str
    status: str
    created_at: datetime
    estimated_completion_time: Optional[datetime] = None
    progress: float = 0.0
    current_stage: Optional[str] = None


class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str
    status: str  # pending, running, completed, failed
    progress: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time_ms: Optional[int] = None
    error_message: Optional[str] = None
    result_summary: Optional[str] = None


class DocumentGenerationRequest(BaseModel):
    """文档生成请求"""
    requirement_analysis_id: str
    retrieved_content: Dict[str, List[Dict[str, Any]]]
    format_type: str = Field(default="markdown", description="输出格式")
    include_references: bool = Field(default=True, description="是否包含引用")
    include_metadata: bool = Field(default=True, description="是否包含元数据")


class DocumentResponse(BaseModel):
    """文档响应"""
    document_id: str
    title: str
    format_type: str
    content: str
    word_count: int
    quality_score: float
    sections_count: int
    references_count: int
    generated_at: datetime
    download_url: Optional[str] = None


class SearchRequest(BaseModel):
    """搜索请求"""
    query: str = Field(..., description="搜索查询")
    sources: Optional[List[str]] = Field(default=[], description="搜索源")
    max_results: int = Field(default=10, description="最大结果数")
    language: str = Field(default="zh", description="语言")


class SearchResponse(BaseModel):
    """搜索响应"""
    query: str
    total_results: int
    results: List[Dict[str, Any]]
    search_time_ms: int
    sources_used: List[str]


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str = "healthy"
    version: str
    timestamp: datetime = Field(default_factory=datetime.now)
    services: Dict[str, str] = Field(default_factory=dict)
    uptime_seconds: float


class ConfigResponse(BaseModel):
    """配置响应"""
    llm_providers: List[str]
    search_engines: List[str]
    supported_formats: List[str]
    max_concurrent_tasks: int
    default_timeout_seconds: int


class MetricsResponse(BaseModel):
    """指标响应"""
    total_workflows: int
    active_workflows: int
    completed_workflows: int
    failed_workflows: int
    average_completion_time_seconds: float
    success_rate: float
    last_24h_requests: int


class WorkflowListResponse(BaseModel):
    """工作流列表响应"""
    workflows: List[WorkflowResponse]
    total_count: int
    page: int
    page_size: int
    has_next: bool


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="排序顺序")


class FilterParams(BaseModel):
    """过滤参数"""
    status: Optional[str] = Field(None, description="状态过滤")
    created_after: Optional[datetime] = Field(None, description="创建时间过滤（之后）")
    created_before: Optional[datetime] = Field(None, description="创建时间过滤（之前）")
    topic_contains: Optional[str] = Field(None, description="主题包含")


class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    workflow_ids: List[str] = Field(..., description="工作流ID列表")
    operation: str = Field(..., description="操作类型")
    parameters: Optional[Dict[str, Any]] = Field(default={}, description="操作参数")


class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    operation: str
    total_count: int
    success_count: int
    failed_count: int
    results: List[Dict[str, Any]]
    errors: List[Dict[str, Any]]


class ExportRequest(BaseModel):
    """导出请求"""
    workflow_ids: Optional[List[str]] = Field(None, description="工作流ID列表")
    format_type: str = Field(default="json", description="导出格式")
    include_content: bool = Field(default=True, description="是否包含内容")
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    date_range: Optional[Dict[str, datetime]] = Field(None, description="日期范围")


class ExportResponse(BaseModel):
    """导出响应"""
    export_id: str
    format_type: str
    file_size_bytes: int
    download_url: str
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.now)
