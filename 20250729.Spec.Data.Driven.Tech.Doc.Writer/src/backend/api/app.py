"""
FastAPI应用主文件

创建和配置FastAPI应用实例。
"""

import time
from datetime import datetime
from typing import Dict, Any
import structlog
from fastapi import FastAPI, Depends
from fastapi.responses import JSONResponse

from src.backend.config import Config
from .middleware import setup_middleware, create_error_handler
from .models import APIResponse, HealthCheckResponse, ConfigResponse

logger = structlog.get_logger(__name__)

# 应用启动时间
app_start_time = time.time()


def create_app(config: Config = None) -> FastAPI:
    """创建FastAPI应用实例"""
    
    if config is None:
        config = Config()
    
    # 创建FastAPI应用
    app = FastAPI(
        title="技术文档写作多智能体系统",
        description="基于多智能体架构的智能技术文档生成系统",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # 设置中间件
    setup_middleware(app, config)
    
    # 设置错误处理器
    create_error_handler(app)
    
    # 根路径
    @app.get("/", response_model=APIResponse)
    async def root():
        """根路径"""
        return APIResponse(
            data={"message": "技术文档写作多智能体系统 API"},
            message="服务正常运行"
        )
    
    # 健康检查
    @app.get("/health", response_model=HealthCheckResponse)
    async def health_check():
        """健康检查"""
        uptime = time.time() - app_start_time
        
        # 检查各个服务状态
        services = {
            "api": "healthy",
            "database": "healthy",  # 这里可以添加实际的数据库检查
            "cache": "healthy",     # 这里可以添加实际的缓存检查
            "llm": "healthy"        # 这里可以添加实际的LLM服务检查
        }
        
        return HealthCheckResponse(
            version="1.0.0",
            services=services,
            uptime_seconds=uptime
        )
    
    # 配置信息
    @app.get("/config", response_model=ConfigResponse)
    async def get_config():
        """获取系统配置信息"""
        return ConfigResponse(
            llm_providers=["openai", "gemini", "deepseek", "qwen"],
            search_engines=["duckduckgo", "tavily"],
            supported_formats=["markdown", "html", "json", "pdf"],
            max_concurrent_tasks=getattr(getattr(config, 'scheduler', None), 'max_concurrent_tasks', 3),
            default_timeout_seconds=getattr(getattr(config, 'workflow', None), 'default_task_timeout_seconds', 60)
        )
    
    # 启动事件
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        logger.info("FastAPI application starting up")
        
        # 这里可以添加启动时的初始化逻辑
        # 例如：数据库连接、缓存初始化等
        
        logger.info("FastAPI application startup completed")
    
    # 关闭事件
    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        logger.info("FastAPI application shutting down")
        
        # 这里可以添加关闭时的清理逻辑
        # 例如：关闭数据库连接、清理缓存等
        
        logger.info("FastAPI application shutdown completed")
    
    logger.info("FastAPI application created successfully")
    return app


# 创建默认应用实例
app = create_app()
