"""
API中间件

提供请求处理、错误处理、日志记录等中间件功能。
"""

import time
import uuid
from typing import Callable
import structlog
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .models import ErrorResponse

logger = structlog.get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始
        start_time = time.time()
        
        logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent")
        )
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成
            logger.info(
                "Request completed",
                request_id=request_id,
                status_code=response.status_code,
                process_time_ms=round(process_time * 1000, 2)
            )
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误
            logger.error(
                "Request failed",
                request_id=request_id,
                error=str(e),
                process_time_ms=round(process_time * 1000, 2)
            )
            
            # 返回错误响应
            error_response = ErrorResponse(
                error="Internal server error",
                error_code="INTERNAL_ERROR",
                request_id=request_id
            )
            
            return JSONResponse(
                status_code=500,
                content=error_response.model_dump(),
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": str(round(process_time * 1000, 2))
                }
            )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""
    
    def __init__(self, app, calls_per_minute: int = 60):
        super().__init__(app)
        self.calls_per_minute = calls_per_minute
        self.client_requests = {}  # 简单的内存存储，生产环境应使用Redis
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期记录
        self._cleanup_expired_records(current_time)
        
        # 检查速率限制
        if self._is_rate_limited(client_ip, current_time):
            error_response = ErrorResponse(
                error="Rate limit exceeded",
                error_code="RATE_LIMIT_EXCEEDED",
                details={"limit": self.calls_per_minute, "window": "1 minute"}
            )
            
            return JSONResponse(
                status_code=429,
                content=error_response.model_dump(),
                headers={"Retry-After": "60"}
            )
        
        # 记录请求
        self._record_request(client_ip, current_time)
        
        return await call_next(request)
    
    def _cleanup_expired_records(self, current_time: float) -> None:
        """清理过期的请求记录"""
        cutoff_time = current_time - 60  # 1分钟前
        
        for client_ip in list(self.client_requests.keys()):
            self.client_requests[client_ip] = [
                timestamp for timestamp in self.client_requests[client_ip]
                if timestamp > cutoff_time
            ]
            
            if not self.client_requests[client_ip]:
                del self.client_requests[client_ip]
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """检查是否超过速率限制"""
        if client_ip not in self.client_requests:
            return False
        
        recent_requests = len(self.client_requests[client_ip])
        return recent_requests >= self.calls_per_minute
    
    def _record_request(self, client_ip: str, current_time: float) -> None:
        """记录请求"""
        if client_ip not in self.client_requests:
            self.client_requests[client_ip] = []
        
        self.client_requests[client_ip].append(current_time)


def setup_middleware(app: FastAPI, config) -> None:
    """设置中间件"""
    
    # CORS中间件
    if hasattr(config, 'api') and hasattr(config.api, 'cors'):
        cors_config = config.api.cors
        app.add_middleware(
            CORSMiddleware,
            allow_origins=cors_config.allow_origins,
            allow_credentials=cors_config.allow_credentials,
            allow_methods=cors_config.allow_methods,
            allow_headers=cors_config.allow_headers,
        )
    else:
        # 默认CORS配置
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE"],
            allow_headers=["*"],
        )
    
    # 受信任主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
    )
    
    # 速率限制中间件
    app.add_middleware(RateLimitMiddleware, calls_per_minute=60)
    
    # 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    logger.info("Middleware setup completed")


def create_error_handler(app: FastAPI) -> None:
    """创建全局错误处理器"""
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        request_id = getattr(request.state, 'request_id', None)
        
        error_response = ErrorResponse(
            error=str(exc),
            error_code="VALIDATION_ERROR",
            request_id=request_id
        )
        
        return JSONResponse(
            status_code=400,
            content=error_response.model_dump()
        )
    
    @app.exception_handler(FileNotFoundError)
    async def file_not_found_handler(request: Request, exc: FileNotFoundError):
        request_id = getattr(request.state, 'request_id', None)
        
        error_response = ErrorResponse(
            error="Resource not found",
            error_code="NOT_FOUND",
            request_id=request_id
        )
        
        return JSONResponse(
            status_code=404,
            content=error_response.model_dump()
        )
    
    @app.exception_handler(PermissionError)
    async def permission_error_handler(request: Request, exc: PermissionError):
        request_id = getattr(request.state, 'request_id', None)
        
        error_response = ErrorResponse(
            error="Permission denied",
            error_code="PERMISSION_DENIED",
            request_id=request_id
        )
        
        return JSONResponse(
            status_code=403,
            content=error_response.model_dump()
        )
    
    @app.exception_handler(TimeoutError)
    async def timeout_error_handler(request: Request, exc: TimeoutError):
        request_id = getattr(request.state, 'request_id', None)
        
        error_response = ErrorResponse(
            error="Request timeout",
            error_code="TIMEOUT",
            request_id=request_id
        )
        
        return JSONResponse(
            status_code=408,
            content=error_response.model_dump()
        )
    
    logger.info("Error handlers setup completed")
