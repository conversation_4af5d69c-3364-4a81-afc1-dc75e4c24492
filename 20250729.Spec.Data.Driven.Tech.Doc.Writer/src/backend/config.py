"""
配置管理模块

实现智能配置系统，支持：
- 分层配置（必需 < 可选 < 高级）
- 环境变量覆盖
- 智能缺省值
- 配置验证
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings
import structlog
from src.backend.utils.env_parser import parse_config_dict

# 初始化日志
logger = structlog.get_logger(__name__)


class LLMConfig(BaseModel):
    """LLM配置模型"""
    provider: str = "openai"
    model: str = "gpt-4o"
    api_key: str
    base_url: Optional[str] = None
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_tokens: int = Field(default=4096, gt=0)
    timeout: int = Field(default=300, gt=0)


class DatabaseConfig(BaseModel):
    """数据库配置模型"""
    type: str = "sqlite"
    url: str = Field(default="sqlite+aiosqlite:///./data/dev.db", env="DATABASE_URL")
    echo: bool = False
    pool_size: int = Field(default=5, gt=0)
    max_overflow: int = Field(default=10, ge=0)
    pool_timeout: int = Field(default=30, gt=0)
    pool_recycle: int = Field(default=3600, gt=0)


class CacheConfig(BaseModel):
    """缓存配置模型"""
    redis_enabled: bool = True
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    filesystem_enabled: bool = True
    filesystem_base_path: str = Field(default="./data/cache", env="CACHE_FILESYSTEM_PATH")
    filesystem_max_size_gb: int = Field(default=10, gt=0)


class ProxyConfig(BaseModel):
    """代理配置模型"""
    http_proxy: Optional[str] = Field(default="http://127.0.0.1:8118/", env="HTTP_PROXY")
    https_proxy: Optional[str] = Field(default="http://127.0.0.1:8118/", env="HTTPS_PROXY")
    no_proxy: str = Field(default="localhost,127.0.0.1", env="NO_PROXY")


class NetworkRetryConfig(BaseModel):
    """网络请求重试配置模型"""
    max_retries: int = Field(default=3, ge=0, le=10, env="NETWORK_MAX_RETRIES", description="最大重试次数")
    initial_delay: float = Field(default=1.0, ge=0.1, le=10.0, env="NETWORK_INITIAL_DELAY", description="初始重试延迟（秒）")
    max_delay: float = Field(default=60.0, ge=1.0, le=300.0, env="NETWORK_MAX_DELAY", description="最大重试延迟（秒）")
    backoff_factor: float = Field(default=2.0, ge=1.0, le=5.0, env="NETWORK_BACKOFF_FACTOR", description="指数退避因子")
    jitter: bool = Field(default=True, env="NETWORK_JITTER", description="是否添加随机抖动")
    retry_on_timeout: bool = Field(default=True, env="NETWORK_RETRY_ON_TIMEOUT", description="超时时是否重试")
    retry_on_connection_error: bool = Field(default=True, env="NETWORK_RETRY_ON_CONNECTION_ERROR", description="连接错误时是否重试")
    retry_on_http_error: bool = Field(default=False, env="NETWORK_RETRY_ON_HTTP_ERROR", description="HTTP错误时是否重试")
    retry_http_status_codes: List[int] = Field(
        default=[429, 502, 503, 504],
        env="NETWORK_RETRY_HTTP_STATUS_CODES",
        description="需要重试的HTTP状态码列表"
    )


class LiteLLMLoggingConfig(BaseModel):
    """LiteLLM 专用日志配置模型"""
    enabled: bool = True
    level: str = "INFO"
    directory: str = "./log"
    filename_pattern: str = "little_llm.{date}.log"
    max_size_mb: int = Field(default=50, gt=0)
    backup_count: int = Field(default=30, gt=0)
    include_request_response: bool = True
    include_metadata: bool = True
    json_format: bool = True


class LoggingConfig(BaseModel):
    """日志配置模型"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="structured", env="LOG_FORMAT")
    console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    file_path: str = Field(default="./log/app.log", env="LOG_FILE_PATH")
    file_max_size_mb: int = Field(default=100, gt=0)
    file_backup_count: int = Field(default=5, ge=0)
    litellm: LiteLLMLoggingConfig = LiteLLMLoggingConfig()


class WorkflowConfig(BaseModel):
    """工作流配置模型"""
    max_concurrent_tasks: int = Field(default=10, gt=0, env="WORKFLOW_MAX_CONCURRENT_TASKS")
    default_task_timeout_seconds: int = Field(default=300, gt=0, env="WORKFLOW_DEFAULT_TASK_TIMEOUT")
    default_retry_count: int = Field(default=3, ge=0, env="WORKFLOW_DEFAULT_RETRY_COUNT")
    enable_checkpoints: bool = Field(default=True, env="WORKFLOW_ENABLE_CHECKPOINTS")
    checkpoint_interval_seconds: int = Field(default=60, gt=0, env="WORKFLOW_CHECKPOINT_INTERVAL")


class SchedulerConfig(BaseModel):
    """调度器配置模型"""
    max_concurrent_tasks: int = Field(default=10, gt=0, env="SCHEDULER_MAX_CONCURRENT_TASKS")
    task_queue_size: int = Field(default=1000, gt=0, env="SCHEDULER_TASK_QUEUE_SIZE")
    heartbeat_interval_seconds: int = Field(default=30, gt=0, env="SCHEDULER_HEARTBEAT_INTERVAL")
    cleanup_interval_seconds: int = Field(default=300, gt=0, env="SCHEDULER_CLEANUP_INTERVAL")


class TraceMallocConfig(BaseModel):
    """Tracemalloc 配置模型"""
    enabled: bool = Field(default=False, env="TRACEMALLOC_ENABLED")
    nframe: int = Field(default=25, gt=0, le=100, env="TRACEMALLOC_NFRAME")


class AsyncioConfig(BaseModel):
    """Asyncio 调试配置模型"""
    debug: bool = Field(default=False, env="ASYNCIO_DEBUG")
    log_slow_callbacks: bool = Field(default=True, env="ASYNCIO_LOG_SLOW_CALLBACKS")
    slow_callback_duration: float = Field(default=0.1, gt=0, env="ASYNCIO_SLOW_CALLBACK_DURATION")


class SearchEnginesConfig(BaseModel):
    """搜索引擎配置模型"""
    primary: str = "tavily"
    fallback: str = "duckduckgo"
    tavily_api_key: Optional[str] = None
    max_results: int = 10


class ResearchConfig(BaseModel):
    """调研配置模型"""
    max_search_results: int = Field(default=60, ge=10, le=200, description="搜索结果最大数量")
    max_source_list_size: int = Field(default=15, ge=5, le=50, description="信息源清单最大数量")
    max_concurrent_requests: int = Field(default=5, ge=1, le=20, description="最大并发请求数")
    search_query_limit: int = Field(default=5, ge=1, le=10, description="搜索查询数量限制")
    primary_keywords_limit: int = Field(default=3, ge=1, le=5, description="主要关键词数量限制")
    technical_terms_limit: int = Field(default=2, ge=1, le=5, description="技术术语数量限制")
    english_keywords_limit: int = Field(default=2, ge=1, le=5, description="英文关键词数量限制")
    information_summary_limit: int = Field(default=10, ge=5, le=20, description="信息摘要处理结果数量限制")


class MarkdownConfig(BaseModel):
    """Markdown转换配置模型"""
    recursion_limit: int = Field(default=5000, ge=1000, le=10000, description="递归限制")
    max_html_depth: int = Field(default=100, ge=10, le=500, description="最大HTML嵌套深度")
    enable_preprocessing: bool = Field(default=True, description="启用HTML预处理")
    fallback_strategies: List[str] = Field(
        default=["preprocessing", "text_extraction"],
        description="降级策略列表"
    )


class ContentProcessingConfig(BaseModel):
    """内容处理配置模型"""
    markdown: MarkdownConfig = MarkdownConfig()


class NetworkConfig(BaseModel):
    """网络配置模型"""
    retry: NetworkRetryConfig = NetworkRetryConfig()
    timeout: float = Field(default=30.0, ge=1.0, le=300.0, env="NETWORK_TIMEOUT", description="网络请求超时时间（秒）")
    verify_ssl: bool = Field(default=True, env="VERIFY_SSL", description="是否验证SSL证书")
    user_agent: str = Field(
        default="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        env="NETWORK_USER_AGENT",
        description="HTTP请求User-Agent - 模拟macOS M1 Chrome浏览器"
    )


class RetrievalConfig(BaseModel):
    """信息检索配置模型"""
    search_engines: SearchEnginesConfig = SearchEnginesConfig()
    research: ResearchConfig = ResearchConfig()


class DebugConfig(BaseModel):
    """调试配置模型"""
    tracemalloc: TraceMallocConfig = TraceMallocConfig()
    asyncio: AsyncioConfig = AsyncioConfig()


class Config(BaseSettings):
    """主配置类"""

    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")

    # LLM配置
    llm_primary: LLMConfig
    llm_fallback: Optional[LLMConfig] = None

    # 数据库配置
    database: DatabaseConfig = DatabaseConfig()

    # 缓存配置
    cache: CacheConfig = CacheConfig()

    # 代理配置
    proxy: ProxyConfig = ProxyConfig()

    # 日志配置
    logging: LoggingConfig = LoggingConfig()

    # 工作流配置
    workflow: WorkflowConfig = WorkflowConfig()

    # 调度器配置
    scheduler: SchedulerConfig = SchedulerConfig()

    # 调试配置
    debug_config: DebugConfig = Field(default_factory=DebugConfig)

    # 信息检索配置
    retrieval: RetrievalConfig = RetrievalConfig()

    # 内容处理配置
    content_processing: ContentProcessingConfig = ContentProcessingConfig()

    # 网络配置
    network: NetworkConfig = NetworkConfig()

    # API配置
    api_host: str = Field(default="127.0.0.1", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")

    # 路径配置
    project_root: Optional[str] = Field(default=None, env="PROJECT_ROOT")
    data_directory: str = Field(default="./data", env="DATA_DIRECTORY")
    output_directory: str = Field(default="./data/output", env="OUTPUT_DIRECTORY")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # 忽略额外字段，避免验证错误
        
    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed = ["development", "testing", "production"]
        if v not in allowed:
            raise ValueError(f"Environment must be one of {allowed}")
        return v
    
    @field_validator("llm_primary", mode="before")
    @classmethod
    def validate_llm_primary(cls, v):
        """验证主LLM配置"""
        if isinstance(v, dict):
            # 确保API密钥存在
            if not v.get("api_key"):
                # 尝试从环境变量获取
                provider = v.get("provider", "openai")
                if provider == "openai":
                    v["api_key"] = os.getenv("OPENAI_API_KEY", "")
                elif provider == "anthropic":
                    v["api_key"] = os.getenv("ANTHROPIC_API_KEY", "")

                if not v["api_key"]:
                    raise ValueError(f"API key required for {provider}")
        return v


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        self.config_path = config_path or Path("config.yaml")
        self._config: Optional[Config] = None
    
    async def load_config(self, config_path: Optional[Union[str, Path]] = None) -> Config:
        """加载配置"""
        if config_path:
            self.config_path = Path(config_path)
        
        # 加载YAML配置文件
        config_data = {}
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f) or {}
                # 解析环境变量语法
                config_data = parse_config_dict(raw_config)
        
        # 应用环境特定配置
        environment = os.getenv("ENVIRONMENT", "development")
        if "environment" in config_data and environment in config_data["environment"]:
            env_config = config_data["environment"][environment]
            config_data = self._merge_config(config_data, env_config)
        
        # 处理LLM配置
        config_data = self._process_llm_config(config_data)

        # 处理调试配置
        config_data = self._process_debug_config(config_data)

        # 创建配置实例
        self._config = Config(**config_data)

        # 应用代理配置
        self._apply_proxy_config()

        # 初始化 LiteLLM 日志处理器
        self._initialize_litellm_logging()

        # 初始化 tracemalloc
        self._initialize_tracemalloc()

        return self._config
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def _process_llm_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理LLM配置"""
        logger.debug("Processing LLM config", config_keys=list(config_data.keys()))

        if "llm" in config_data:
            llm_config = config_data["llm"]
            logger.debug("Found LLM config", llm_keys=list(llm_config.keys()))

            # 处理主LLM配置
            if "primary" in llm_config:
                config_data["llm_primary"] = llm_config["primary"]
                logger.debug("Set llm_primary", provider=llm_config["primary"].get("provider"))

            # 处理备用LLM配置
            if "fallback" in llm_config:
                config_data["llm_fallback"] = llm_config["fallback"]
                logger.debug("Set llm_fallback", provider=llm_config["fallback"].get("provider"))

            # 移除原始llm配置以避免验证错误
            del config_data["llm"]
            logger.debug("Removed original llm config")

        return config_data

    def _process_debug_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理调试配置"""
        if "debug" in config_data:
            debug_config = config_data["debug"]
            logger.debug("Found debug config", debug_keys=list(debug_config.keys()) if debug_config else [])

            # 将 debug 配置映射到 debug_config
            config_data["debug_config"] = debug_config

            # 移除原始 debug 配置以避免验证错误
            del config_data["debug"]
            logger.debug("Processed debug config")

        return config_data

    def _apply_proxy_config(self) -> None:
        """应用代理配置"""
        if self._config and self._config.proxy:
            if self._config.proxy.http_proxy:
                os.environ["HTTP_PROXY"] = self._config.proxy.http_proxy
            if self._config.proxy.https_proxy:
                os.environ["HTTPS_PROXY"] = self._config.proxy.https_proxy
            if self._config.proxy.no_proxy:
                os.environ["NO_PROXY"] = self._config.proxy.no_proxy

    def _initialize_litellm_logging(self) -> None:
        """初始化 LiteLLM 日志处理器"""
        if not self._config:
            return

        try:
            from src.backend.llm.logging_handler import initialize_litellm_logging

            # 构造配置字典
            config_dict = {
                'logging': {
                    'litellm': self._config.logging.litellm.model_dump()
                }
            }

            # 初始化 LiteLLM 日志处理器
            initialize_litellm_logging(config_dict)
            logger.info("LiteLLM logging handler initialized successfully")

        except ImportError as e:
            logger.warning(f"Failed to import LiteLLM logging handler: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize LiteLLM logging handler: {e}")

    def _initialize_tracemalloc(self) -> None:
        """初始化 tracemalloc"""
        if not self._config:
            return

        try:
            from src.backend.debug import configure_tracemalloc, start_tracemalloc

            # 配置 tracemalloc
            debug_config = self._config.debug_config
            configure_tracemalloc(
                config_enabled=debug_config.tracemalloc.enabled,
                nframe=debug_config.tracemalloc.nframe
            )

            # 启动 tracemalloc
            if start_tracemalloc():
                logger.info(
                    "Tracemalloc initialized successfully",
                    enabled=debug_config.tracemalloc.enabled,
                    nframe=debug_config.tracemalloc.nframe
                )
            else:
                logger.debug("Tracemalloc not started (disabled by configuration)")

        except ImportError as e:
            logger.warning(f"Failed to import tracemalloc manager: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize tracemalloc: {e}")

    def get_config(self) -> Config:
        """获取当前配置"""
        if self._config is None:
            raise RuntimeError("Configuration not loaded. Call load_config() first.")
        return self._config


# 全局配置管理器实例
_config_manager = ConfigManager()


async def get_config(config_path: Optional[Union[str, Path]] = None) -> Config:
    """获取配置实例"""
    return await _config_manager.load_config(config_path)


def get_current_config() -> Config:
    """获取当前配置（同步）"""
    return _config_manager.get_config()
