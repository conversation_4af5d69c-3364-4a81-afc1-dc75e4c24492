"""
文档格式化器

负责将生成的文档转换为不同的输出格式。
"""

from typing import Dict, Any, Optional
import structlog
from datetime import datetime
from pathlib import Path

from src.backend.models.document import DocumentResult

logger = structlog.get_logger(__name__)


class DocumentFormatter:
    """文档格式化器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    async def format_document(
        self, 
        document: DocumentResult, 
        format_type: str = "markdown"
    ) -> Dict[str, Any]:
        """
        格式化文档
        
        Args:
            document: 文档结果
            format_type: 输出格式类型
            
        Returns:
            格式化结果
        """
        self.logger.info("Starting document formatting", 
                        format_type=format_type,
                        document_id=str(document.id))
        
        try:
            if format_type == "markdown":
                content = self._format_markdown(document)
            elif format_type == "html":
                content = self._format_html(document)
            elif format_type == "json":
                content = self._format_json(document)
            else:
                raise ValueError(f"Unsupported format type: {format_type}")
            
            result = {
                "success": True,
                "format_type": format_type,
                "content": content,
                "metadata": {
                    "document_id": str(document.id),
                    "title": document.title,
                    "word_count": document.word_count,
                    "sections_count": len(document.sections),
                    "references_count": len(document.references),
                    "generated_at": datetime.now().isoformat()
                }
            }
            
            self.logger.info("Document formatting completed",
                           format_type=format_type,
                           content_length=len(content))
            
            return result
            
        except Exception as e:
            self.logger.error("Document formatting failed",
                            format_type=format_type,
                            error=str(e))
            return {
                "success": False,
                "error": str(e),
                "format_type": format_type,
                "content": ""
            }
    
    def _format_markdown(self, document: DocumentResult) -> str:
        """格式化为Markdown"""
        lines = []
        
        # 标题
        lines.append(f"# {document.title}")
        lines.append("")
        
        # 元数据
        lines.append("---")
        lines.append(f"生成时间: {document.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"字数统计: {document.word_count or document.get_total_word_count()}")
        lines.append(f"质量评分: {document.quality_score:.2f}" if document.quality_score else "质量评分: 未评估")
        if document.keywords:
            lines.append(f"关键词: {', '.join(document.keywords)}")
        lines.append("---")
        lines.append("")
        
        # 摘要
        if document.abstract:
            lines.append("## 摘要")
            lines.append("")
            lines.append(document.abstract)
            lines.append("")
        
        # 目录
        if document.sections:
            lines.append("## 目录")
            lines.append("")
            for section in document.sections:
                indent = "  " * (section.level - 1)
                lines.append(f"{indent}- [{section.title}](#{self._to_anchor(section.title)})")
            lines.append("")
        
        # 章节内容
        for section in document.sections:
            # 章节标题
            header_prefix = "#" * (section.level + 1)
            lines.append(f"{header_prefix} {section.title}")
            lines.append("")
            
            # 章节内容
            lines.append(section.content)
            lines.append("")
        
        # 参考文献
        if document.references:
            lines.append("## 参考文献")
            lines.append("")
            for i, ref in enumerate(document.references, 1):
                citation = self._format_reference(ref, i)
                lines.append(citation)
            lines.append("")
        
        # 附录
        lines.append("## 附录")
        lines.append("")
        lines.append("### 生成信息")
        lines.append("")
        lines.append(f"- 文档ID: {document.id}")
        lines.append(f"- 用户请求ID: {document.user_request_id}")
        lines.append(f"- 生成方法: {document.generation_method}")
        lines.append(f"- 语言: {document.language}")
        
        return "\n".join(lines)
    
    def _format_html(self, document: DocumentResult) -> str:
        """格式化为HTML"""
        html_parts = []
        
        # HTML头部
        html_parts.append("<!DOCTYPE html>")
        html_parts.append("<html lang='zh-CN'>")
        html_parts.append("<head>")
        html_parts.append("<meta charset='UTF-8'>")
        html_parts.append("<meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_parts.append(f"<title>{document.title}</title>")
        html_parts.append(self._get_html_styles())
        html_parts.append("</head>")
        html_parts.append("<body>")
        
        # 文档内容
        html_parts.append("<div class='document'>")
        
        # 标题
        html_parts.append(f"<h1>{document.title}</h1>")
        
        # 元数据
        html_parts.append("<div class='metadata'>")
        html_parts.append(f"<p><strong>生成时间:</strong> {document.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>")
        html_parts.append(f"<p><strong>字数统计:</strong> {document.word_count or document.get_total_word_count()}</p>")
        if document.quality_score:
            html_parts.append(f"<p><strong>质量评分:</strong> {document.quality_score:.2f}</p>")
        if document.keywords:
            html_parts.append(f"<p><strong>关键词:</strong> {', '.join(document.keywords)}</p>")
        html_parts.append("</div>")
        
        # 摘要
        if document.abstract:
            html_parts.append("<h2>摘要</h2>")
            html_parts.append(f"<div class='abstract'>{document.abstract}</div>")
        
        # 目录
        if document.sections:
            html_parts.append("<h2>目录</h2>")
            html_parts.append("<ul class='toc'>")
            for section in document.sections:
                anchor = self._to_anchor(section.title)
                html_parts.append(f"<li><a href='#{anchor}'>{section.title}</a></li>")
            html_parts.append("</ul>")
        
        # 章节内容
        for section in document.sections:
            anchor = self._to_anchor(section.title)
            html_parts.append(f"<h{section.level + 1} id='{anchor}'>{section.title}</h{section.level + 1}>")
            
            # 将Markdown内容转换为HTML（简单实现）
            content_html = self._markdown_to_html_simple(section.content)
            html_parts.append(f"<div class='section-content'>{content_html}</div>")
        
        # 参考文献
        if document.references:
            html_parts.append("<h2>参考文献</h2>")
            html_parts.append("<ol class='references'>")
            for ref in document.references:
                citation_html = self._format_reference_html(ref)
                html_parts.append(f"<li>{citation_html}</li>")
            html_parts.append("</ol>")
        
        html_parts.append("</div>")
        html_parts.append("</body>")
        html_parts.append("</html>")
        
        return "\n".join(html_parts)
    
    def _format_json(self, document: DocumentResult) -> str:
        """格式化为JSON"""
        import json
        return json.dumps(document.model_dump(), ensure_ascii=False, indent=2)
    
    def _to_anchor(self, text: str) -> str:
        """将文本转换为锚点"""
        import re
        # 移除特殊字符，保留中文、英文和数字
        anchor = re.sub(r'[^\w\u4e00-\u9fff]', '-', text)
        return anchor.lower()
    
    def _format_reference(self, ref, index: int) -> str:
        """格式化引用（Markdown）"""
        parts = [f"{index}."]
        
        if ref.authors:
            authors_str = ", ".join(ref.authors[:3])
            if len(ref.authors) > 3:
                authors_str += " et al."
            parts.append(authors_str + ".")
        
        parts.append(f"*{ref.title}*.")
        
        if ref.publication_date:
            parts.append(f"({ref.publication_date.year}).")
        
        if ref.url:
            parts.append(f"Available at: {ref.url}")
        
        return " ".join(parts)
    
    def _format_reference_html(self, ref) -> str:
        """格式化引用（HTML）"""
        parts = []
        
        if ref.authors:
            authors_str = ", ".join(ref.authors[:3])
            if len(ref.authors) > 3:
                authors_str += " et al."
            parts.append(f"<strong>{authors_str}</strong>")
        
        parts.append(f"<em>{ref.title}</em>")
        
        if ref.publication_date:
            parts.append(f"({ref.publication_date.year})")
        
        if ref.url:
            parts.append(f"<a href='{ref.url}' target='_blank'>链接</a>")
        
        return ". ".join(parts) + "."
    
    def _markdown_to_html_simple(self, markdown_content: str) -> str:
        """简单的Markdown到HTML转换"""
        import re
        
        html = markdown_content
        
        # 标题
        html = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        
        # 粗体和斜体
        html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
        html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)
        
        # 链接
        html = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', html)
        
        # 段落
        paragraphs = html.split('\n\n')
        html_paragraphs = []
        for p in paragraphs:
            p = p.strip()
            if p and not p.startswith('<'):
                html_paragraphs.append(f'<p>{p}</p>')
            else:
                html_paragraphs.append(p)
        
        return '\n'.join(html_paragraphs)
    
    def _get_html_styles(self) -> str:
        """获取HTML样式"""
        return """
<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.document {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    color: #34495e;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 5px;
}

.metadata {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.abstract {
    background: #e8f4fd;
    padding: 20px;
    border-left: 4px solid #3498db;
    margin: 20px 0;
    font-style: italic;
}

.toc {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
}

.section-content {
    margin: 20px 0;
}

.references {
    font-size: 0.9em;
}

.references li {
    margin-bottom: 10px;
}
</style>
"""
    
    async def save_to_file(
        self, 
        formatted_result: Dict[str, Any], 
        output_path: Optional[str] = None
    ) -> str:
        """
        保存格式化结果到文件
        
        Args:
            formatted_result: 格式化结果
            output_path: 输出路径
            
        Returns:
            保存的文件路径
        """
        if not formatted_result.get("success"):
            raise ValueError("Cannot save failed formatting result")
        
        # 确定文件路径
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            format_type = formatted_result["format_type"]
            title = formatted_result["metadata"]["title"]
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_title = safe_title.replace(' ', '_')[:50]
            
            extension = {
                "markdown": "md",
                "html": "html", 
                "json": "json"
            }.get(format_type, "txt")
            
            output_path = f"output/{safe_title}_{timestamp}.{extension}"
        
        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(formatted_result["content"])
        
        self.logger.info("Document saved to file", 
                        output_path=str(output_file),
                        format_type=formatted_result["format_type"])
        
        return str(output_file)
