"""
文档生成器

负责根据检索到的信息生成结构化的技术文档。
"""

from typing import List, Dict, Any, Optional
import structlog
from datetime import datetime
import re

from src.backend.models.document import DocumentResult, DocumentSection, Reference
from src.backend.models.request import RequirementAnalysis
from src.backend.llm.providers import LLMProviderFactory

logger = structlog.get_logger(__name__)


class DocumentGenerator:
    """文档生成器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
        
        # 创建LLM提供者
        if hasattr(config, 'llm_primary') and config.llm_primary:
            llm_config_dict = config.llm_primary.model_dump()
            self.llm_provider = LLMProviderFactory.create_provider(llm_config_dict)
        else:
            self.llm_provider = None
            self.logger.warning("No LLM provider configured for document generation")
    
    async def generate_document(
        self, 
        requirement_analysis: RequirementAnalysis,
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> DocumentResult:
        """
        生成技术文档
        
        Args:
            requirement_analysis: 需求分析结果
            retrieved_content: 检索到的内容
            
        Returns:
            DocumentResult: 生成的文档
        """
        self.logger.info("Starting document generation", 
                        user_request_id=requirement_analysis.user_request_id)
        
        try:
            # 1. 生成文档标题
            title = await self._generate_title(requirement_analysis)
            
            # 2. 生成摘要
            abstract = await self._generate_abstract(requirement_analysis, retrieved_content)
            
            # 3. 生成章节
            sections = await self._generate_sections(requirement_analysis, retrieved_content)
            
            # 4. 提取引用
            references = self._extract_references(retrieved_content)
            
            # 5. 提取关键词
            keywords = self._extract_keywords(requirement_analysis, retrieved_content)
            
            # 创建文档结果
            document = DocumentResult(
                title=title,
                abstract=abstract,
                sections=sections,
                references=references,
                keywords=keywords,
                user_request_id=requirement_analysis.user_request_id,
                format_type="markdown",
                generation_method="multi_agent"
            )
            
            # 计算质量评分
            document.quality_score = self._calculate_quality_score(document)
            document.word_count = document.get_total_word_count()
            
            self.logger.info("Document generation completed",
                           user_request_id=requirement_analysis.user_request_id,
                           sections_count=len(sections),
                           word_count=document.word_count,
                           quality_score=document.quality_score)
            
            return document
            
        except Exception as e:
            self.logger.error("Document generation failed",
                            user_request_id=requirement_analysis.user_request_id,
                            error=str(e))
            raise
    
    async def _generate_title(self, requirement_analysis: RequirementAnalysis) -> str:
        """生成文档标题"""
        if not self.llm_provider:
            return f"{requirement_analysis.parsed_topic} - 技术调研报告"
        
        try:
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的技术文档标题生成专家。请根据用户的调研需求生成一个简洁、专业的技术文档标题。"
                },
                {
                    "role": "user",
                    "content": f"""
请为以下技术调研需求生成一个专业的文档标题：

主题：{requirement_analysis.parsed_topic}
目标受众：{requirement_analysis.target_audience}
调研目标：{', '.join(requirement_analysis.research_objectives)}

要求：
1. 标题应该简洁明了，不超过30个字符
2. 体现技术性和专业性
3. 包含核心关键词
4. 适合{requirement_analysis.target_audience}阅读

请直接返回标题，不要包含其他内容。
"""
                }
            ]
            
            title = await self.llm_provider.generate_response(messages, max_tokens=100)
            return title.strip().strip('"').strip("'")
            
        except Exception as e:
            self.logger.warning("Failed to generate title with LLM", error=str(e))
            return f"{requirement_analysis.parsed_topic} - 技术调研报告"
    
    async def _generate_abstract(
        self, 
        requirement_analysis: RequirementAnalysis,
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """生成文档摘要"""
        if not self.llm_provider:
            return f"本报告针对{requirement_analysis.parsed_topic}进行了全面的技术调研，分析了相关技术现状、发展趋势和应用前景。"
        
        try:
            # 准备内容摘要
            content_summary = self._prepare_content_summary(retrieved_content)
            
            messages = [
                {
                    "role": "system",
                    "content": "你是一个专业的技术文档摘要生成专家。请根据调研需求和检索到的内容生成一个简洁、全面的文档摘要。"
                },
                {
                    "role": "user",
                    "content": f"""
请为以下技术调研生成一个专业的文档摘要：

调研主题：{requirement_analysis.parsed_topic}
调研目标：{', '.join(requirement_analysis.research_objectives)}
目标受众：{requirement_analysis.target_audience}

检索到的内容概要：
{content_summary}

要求：
1. 摘要长度控制在200-300字
2. 概括调研的主要内容和发现
3. 体现技术性和专业性
4. 突出核心价值和意义

请直接返回摘要内容，不要包含其他内容。
"""
                }
            ]
            
            abstract = await self.llm_provider.generate_response(messages, max_tokens=500)
            return abstract.strip()
            
        except Exception as e:
            self.logger.warning("Failed to generate abstract with LLM", error=str(e))
            return f"本报告针对{requirement_analysis.parsed_topic}进行了全面的技术调研，分析了相关技术现状、发展趋势和应用前景。"
    
    async def _generate_sections(
        self,
        requirement_analysis: RequirementAnalysis,
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> List[DocumentSection]:
        """生成文档章节"""
        sections = []
        
        # 定义标准章节结构
        section_templates = [
            {
                "title": "技术概述",
                "content_types": ["academic", "industry_report"],
                "focus": "技术定义、核心概念、基本原理"
            },
            {
                "title": "技术现状分析", 
                "content_types": ["academic", "industry_report", "news"],
                "focus": "当前技术发展水平、主要厂商、市场情况"
            },
            {
                "title": "应用场景与案例",
                "content_types": ["industry_report", "news", "open_source"],
                "focus": "实际应用场景、成功案例、实施经验"
            },
            {
                "title": "技术挑战与限制",
                "content_types": ["academic", "government"],
                "focus": "技术难点、局限性、风险因素"
            },
            {
                "title": "发展趋势与展望",
                "content_types": ["academic", "industry_report", "news"],
                "focus": "未来发展方向、技术趋势、市场预测"
            }
        ]
        
        for i, template in enumerate(section_templates):
            try:
                section = await self._generate_section(
                    template, 
                    requirement_analysis,
                    retrieved_content,
                    order=i+1
                )
                if section:
                    sections.append(section)
            except Exception as e:
                self.logger.warning("Failed to generate section", 
                                  section_title=template["title"], 
                                  error=str(e))
        
        return sections
    
    async def _generate_section(
        self,
        template: Dict[str, Any],
        requirement_analysis: RequirementAnalysis,
        retrieved_content: Dict[str, List[Dict[str, Any]]],
        order: int
    ) -> Optional[DocumentSection]:
        """生成单个章节"""
        if not self.llm_provider:
            # 简单的内容聚合
            content = self._aggregate_content_simple(template, retrieved_content)
            if not content:
                return None
                
            return DocumentSection(
                section_id=f"section_{order}",
                title=template["title"],
                content=content,
                level=1,
                order=order
            )
        
        try:
            # 准备相关内容
            relevant_content = self._filter_relevant_content(template, retrieved_content)
            if not relevant_content:
                return None
            
            messages = [
                {
                    "role": "system",
                    "content": f"你是一个专业的技术文档写作专家。请根据提供的资料生成'{template['title']}'章节的内容。"
                },
                {
                    "role": "user",
                    "content": f"""
请为技术调研报告生成'{template['title']}'章节：

调研主题：{requirement_analysis.parsed_topic}
章节重点：{template['focus']}
目标受众：{requirement_analysis.target_audience}

相关资料：
{relevant_content}

要求：
1. 内容长度控制在800-1200字
2. 结构清晰，逻辑性强
3. 使用Markdown格式
4. 包含具体的技术细节和数据
5. 适合{requirement_analysis.target_audience}阅读

请直接返回章节内容，不要包含章节标题。
"""
                }
            ]
            
            content = await self.llm_provider.generate_response(messages, max_tokens=2000)
            
            return DocumentSection(
                section_id=f"section_{order}",
                title=template["title"],
                content=content.strip(),
                level=1,
                order=order
            )
            
        except Exception as e:
            self.logger.warning("Failed to generate section with LLM", 
                              section_title=template["title"], 
                              error=str(e))
            return None
    
    def _prepare_content_summary(self, retrieved_content: Dict[str, List[Dict[str, Any]]]) -> str:
        """准备内容摘要"""
        summary_parts = []
        
        for source_type, items in retrieved_content.items():
            if items:
                count = len(items)
                titles = [item.get('title', '')[:50] + '...' for item in items[:3]]
                summary_parts.append(f"{source_type}: {count}篇文档，包括{', '.join(titles)}")
        
        return '\n'.join(summary_parts)
    
    def _filter_relevant_content(
        self, 
        template: Dict[str, Any], 
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """过滤相关内容"""
        relevant_items = []
        
        for content_type in template["content_types"]:
            items = retrieved_content.get(content_type, [])
            for item in items[:2]:  # 每种类型最多取2篇
                title = item.get('title', '')
                content = item.get('content', '')[:500]  # 限制长度
                relevant_items.append(f"标题：{title}\n内容：{content}")
        
        return '\n\n'.join(relevant_items)
    
    def _aggregate_content_simple(
        self, 
        template: Dict[str, Any], 
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> str:
        """简单的内容聚合（无LLM）"""
        content_parts = []
        
        for content_type in template["content_types"]:
            items = retrieved_content.get(content_type, [])
            if items:
                content_parts.append(f"## {content_type.replace('_', ' ').title()}")
                for item in items[:2]:
                    title = item.get('title', '')
                    content = item.get('content', '')[:300]
                    content_parts.append(f"### {title}")
                    content_parts.append(content)
        
        return '\n\n'.join(content_parts) if content_parts else ""
    
    def _extract_references(self, retrieved_content: Dict[str, List[Dict[str, Any]]]) -> List[Reference]:
        """提取引用"""
        references = []

        # 映射source_type到允许的值
        source_type_mapping = {
            'academic': 'academic',
            'industry_report': 'industry',
            'news': 'news',
            'government': 'government',
            'open_source': 'opensource',
            'book': 'book',
            'website': 'website'
        }

        for source_type, items in retrieved_content.items():
            # 映射到允许的source_type
            mapped_source_type = source_type_mapping.get(source_type, 'website')

            for item in items:
                reference = Reference(
                    title=item.get('title', ''),
                    authors=item.get('authors', []),
                    url=item.get('url', ''),
                    source_type=mapped_source_type,
                    publication_date=datetime.now()
                )
                references.append(reference)

        return references
    
    def _extract_keywords(
        self, 
        requirement_analysis: RequirementAnalysis,
        retrieved_content: Dict[str, List[Dict[str, Any]]]
    ) -> List[str]:
        """提取关键词"""
        keywords = set()
        
        # 从需求分析中提取
        keywords.add(requirement_analysis.parsed_topic)
        
        # 从内容中提取（简单实现）
        for items in retrieved_content.values():
            for item in items:
                title = item.get('title', '')
                # 简单的关键词提取
                words = re.findall(r'\b[A-Za-z\u4e00-\u9fff]{3,}\b', title)
                keywords.update(words[:3])
        
        return list(keywords)[:10]  # 限制关键词数量
    
    def _calculate_quality_score(self, document: DocumentResult) -> float:
        """计算文档质量评分"""
        score = 0.0
        
        # 基础分数
        if document.title:
            score += 0.1
        if document.abstract:
            score += 0.2
        
        # 章节评分
        if document.sections:
            score += 0.4
            # 章节内容质量
            avg_section_length = sum(len(s.content) for s in document.sections) / len(document.sections)
            if avg_section_length > 500:
                score += 0.2
        
        # 引用评分
        if document.references:
            score += 0.1
        
        return min(score, 1.0)
