"""
网页内容处理器

基于 httpx、readabilipy 和 markdownify 的完整网页内容处理管道。
支持异步抓取、HTML清洁、Markdown转换和质量评估。
"""

import asyncio
import json
import sys
import random
import structlog
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

import httpx
import aiofiles
from bs4 import BeautifulSoup
from readabilipy import simple_json_from_html_string
from markdownify import markdownify as md

from src.backend.agents.url_cache_manager import URLCacheManager
from src.backend.utils.content_file_manager import ContentFileManager
from src.backend.config import Config, NetworkRetryConfig

logger = structlog.get_logger(__name__)


class WebScraper:
    """异步网页抓取器"""

    def __init__(self, proxy_config: Optional[str] = None, verify_ssl: bool = True, retry_config: Optional[NetworkRetryConfig] = None):
        """
        Initialize web scraper with proxy configuration

        Args:
            proxy_config: Proxy URL for network requests
            verify_ssl: Whether to verify SSL certificates (default: True)
            retry_config: Network retry configuration
        """
        self.proxy_url = proxy_config or "http://127.0.0.1:8118/"
        self.verify_ssl = verify_ssl
        self.retry_config = retry_config or NetworkRetryConfig()

        # Configure httpx client with proxy and SSL settings
        # Use macOS M1 Chrome User-Agent to simulate real browser behavior
        macos_m1_chrome_user_agent = (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36"
        )

        client_kwargs = {
            "timeout": 30.0,
            "follow_redirects": True,
            "verify": self.verify_ssl,
            "headers": {
                "User-Agent": macos_m1_chrome_user_agent
            }
        }

        # Only add proxy if configured
        if self.proxy_url:
            client_kwargs["proxy"] = self.proxy_url

        self.client = httpx.AsyncClient(**client_kwargs)

    def _calculate_retry_delay(self, attempt: int) -> float:
        """
        Calculate retry delay with exponential backoff and optional jitter

        Args:
            attempt: Current attempt number (0-based)

        Returns:
            Delay in seconds
        """
        delay = self.retry_config.initial_delay * (self.retry_config.backoff_factor ** attempt)
        delay = min(delay, self.retry_config.max_delay)

        if self.retry_config.jitter:
            # Add random jitter (±25% of delay)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0.1, delay)  # Ensure minimum delay

        return delay

    def _should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        Determine if request should be retried based on exception type and configuration

        Args:
            exception: The exception that occurred
            attempt: Current attempt number (0-based)

        Returns:
            True if should retry, False otherwise
        """
        if attempt >= self.retry_config.max_retries:
            return False

        if isinstance(exception, httpx.TimeoutException):
            return self.retry_config.retry_on_timeout

        if isinstance(exception, httpx.RequestError):
            return self.retry_config.retry_on_connection_error

        if isinstance(exception, httpx.HTTPStatusError):
            # Never retry 4xx client errors - these indicate URL access failure
            if 400 <= exception.response.status_code < 500:
                return False

            if not self.retry_config.retry_on_http_error:
                return False
            return exception.response.status_code in self.retry_config.retry_http_status_codes

        return False

    async def fetch_url(self, url: str) -> Dict[str, Any]:
        """
        Fetch content from URL with retry mechanism

        Args:
            url: Target URL to fetch

        Returns:
            Dictionary containing content and metadata
        """
        logger.info("Starting web content fetch", url=url, max_retries=self.retry_config.max_retries)
        start_time = datetime.now()
        last_exception = None

        for attempt in range(self.retry_config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_retry_delay(attempt - 1)
                    logger.info("Retrying web content fetch",
                               url=url,
                               attempt=attempt + 1,
                               max_retries=self.retry_config.max_retries + 1,
                               delay_seconds=delay)
                    await asyncio.sleep(delay)

                response = await self.client.get(url)

                # Check for 4xx status codes and mark as URL access failure
                if 400 <= response.status_code < 500:
                    logger.warning("URL access failed with 4xx status code",
                                  url=url,
                                  status_code=response.status_code,
                                  reason_phrase=response.reason_phrase,
                                  attempt=attempt + 1)

                    # For 4xx errors, we don't retry and mark as failed
                    return {
                        "url": url,
                        "success": False,
                        "error": f"URL access failed: HTTP {response.status_code} {response.reason_phrase}",
                        "error_type": "client_error",
                        "status_code": response.status_code,
                        "attempts": attempt + 1,
                        "total_time": (datetime.now() - start_time).total_seconds()
                    }

                # For other non-2xx status codes, use standard error handling
                response.raise_for_status()

                # Calculate fetch metrics
                fetch_time = (datetime.now() - start_time).total_seconds()
                content_size = len(response.content)

                # Log fetch metrics
                logger.info("Web content fetch completed",
                           url=url,
                           status_code=response.status_code,
                           content_size_bytes=content_size,
                           fetch_time_seconds=fetch_time,
                           attempts=attempt + 1)

                return {
                    "url": url,
                    "content": response.text,
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "content_size": content_size,
                    "fetch_time": fetch_time,
                    "timestamp": datetime.now().isoformat(),
                    "attempts": attempt + 1,
                    "success": True
                }

            except (httpx.TimeoutException, httpx.HTTPStatusError, httpx.RequestError) as e:
                last_exception = e

                if not self._should_retry(e, attempt):
                    break

                logger.warning("Web content fetch failed, will retry",
                              url=url,
                              attempt=attempt + 1,
                              error=str(e),
                              error_type=type(e).__name__)

            except Exception as e:
                last_exception = e
                logger.error("Web content fetch unexpected error", url=url, error=str(e))
                break

        # All retries exhausted or non-retryable error occurred
        total_time = (datetime.now() - start_time).total_seconds()

        if isinstance(last_exception, httpx.TimeoutException):
            logger.error("Web content fetch timeout after retries",
                        url=url,
                        error=str(last_exception),
                        total_attempts=attempt + 1,
                        total_time_seconds=total_time)
            return {
                "url": url,
                "success": False,
                "error": f"Timeout after {attempt + 1} attempts: {str(last_exception)}",
                "error_type": "timeout",
                "attempts": attempt + 1,
                "total_time": total_time
            }
        elif isinstance(last_exception, httpx.HTTPStatusError):
            status_code = last_exception.response.status_code

            # Special handling for 4xx client errors
            if 400 <= status_code < 500:
                logger.warning("URL access failed with 4xx status code (final)",
                              url=url,
                              status_code=status_code,
                              reason_phrase=last_exception.response.reason_phrase,
                              total_attempts=attempt + 1,
                              total_time_seconds=total_time)
                return {
                    "url": url,
                    "success": False,
                    "error": f"URL access failed: HTTP {status_code} {last_exception.response.reason_phrase}",
                    "error_type": "client_error",
                    "status_code": status_code,
                    "attempts": attempt + 1,
                    "total_time": total_time
                }
            else:
                # For 5xx and other HTTP errors
                logger.error("Web content fetch HTTP error after retries",
                            url=url,
                            status_code=status_code,
                            error=str(last_exception),
                            total_attempts=attempt + 1,
                            total_time_seconds=total_time)
                return {
                    "url": url,
                    "success": False,
                    "error": f"HTTP {status_code} after {attempt + 1} attempts: {str(last_exception)}",
                    "error_type": "http_error",
                    "status_code": status_code,
                    "attempts": attempt + 1,
                    "total_time": total_time
                }
        elif isinstance(last_exception, httpx.RequestError):
            logger.error("Web content fetch request error after retries",
                        url=url,
                        error=str(last_exception),
                        total_attempts=attempt + 1,
                        total_time_seconds=total_time)
            return {
                "url": url,
                "success": False,
                "error": f"Request error after {attempt + 1} attempts: {str(last_exception)}",
                "error_type": "request_error",
                "attempts": attempt + 1,
                "total_time": total_time
            }
        else:
            logger.error("Web content fetch unexpected error after retries",
                        url=url,
                        error=str(last_exception),
                        total_attempts=attempt + 1,
                        total_time_seconds=total_time)
            return {
                "url": url,
                "success": False,
                "error": f"Unexpected error after {attempt + 1} attempts: {str(last_exception)}",
                "error_type": "unexpected_error",
                "attempts": attempt + 1,
                "total_time": total_time
            }
    
    async def fetch_multiple(self, urls: List[str], max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """
        Fetch multiple URLs concurrently
        
        Args:
            urls: List of URLs to fetch
            max_concurrent: Maximum concurrent requests
            
        Returns:
            List of fetch results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.fetch_url(url)
        
        logger.info("Starting concurrent web content fetch",
                   total_urls=len(urls),
                   max_concurrent=max_concurrent)
        
        tasks = [fetch_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error("Fetch task failed", url=urls[i], error=str(result))
                processed_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": f"Task failed: {str(result)}",
                    "error_type": "task_error"
                })
            else:
                processed_results.append(result)
        
        successful_count = sum(1 for r in processed_results if r.get("success", False))
        logger.info("Concurrent web content fetch completed",
                   total_urls=len(urls),
                   successful_count=successful_count,
                   failed_count=len(urls) - successful_count)
        
        return processed_results
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


class HTMLCleaner:
    """HTML 内容清洁器"""
    
    def __init__(self):
        """Initialize HTML cleaner"""
        pass
    
    def clean_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Clean HTML content using readabilipy
        
        Args:
            html_content: Raw HTML content
            url: Source URL for context
            
        Returns:
            Cleaned content with metadata
        """
        try:
            logger.info("Starting HTML cleaning", url=url)
            
            # Use readabilipy to extract main content
            article = simple_json_from_html_string(
                html_content,
                use_readability=True
            )
            
            if not article or not article.get("content"):
                logger.warning("No content extracted from HTML", url=url)
                return {
                    "url": url,
                    "title": "",
                    "content": "",
                    "text_content": "",
                    "success": False,
                    "error": "No content extracted"
                }
            
            # Extract key information with proper None handling
            title = article.get("title") or ""
            content = article.get("content") or ""
            text_content = article.get("plain_text") or ""

            # Handle case where plain_text might be a list
            if isinstance(text_content, list):
                text_content = " ".join(str(item) for item in text_content)
            elif text_content is None:
                text_content = ""

            # Ensure title is a string (additional safety check)
            if title is None:
                title = ""

            # Calculate content metrics
            content_length = len(text_content)
            word_count = len(text_content.split()) if text_content else 0

            logger.info("HTML cleaning completed",
                       url=url,
                       title=title[:50] + "..." if len(title) > 50 else title,
                       content_length=content_length,
                       word_count=word_count)
            
            return {
                "url": url,
                "title": title,
                "content": content,
                "text_content": text_content,
                "content_length": content_length,
                "word_count": word_count,
                "success": True
            }
            
        except Exception as e:
            logger.error("HTML cleaning failed", url=url, error=str(e))
            return {
                "url": url,
                "title": "",
                "content": "",
                "text_content": "",
                "success": False,
                "error": str(e)
            }


class MarkdownConverter:
    """Markdown 转换器 - 支持递归深度控制和降级策略"""

    def __init__(self, config: Optional[Config] = None):
        """Initialize Markdown converter with configuration"""
        self.config = config or Config()

        # 获取Markdown配置
        self.markdown_config = self.config.content_processing.markdown

        # 基础转换选项
        self.conversion_options = {
            "heading_style": "ATX",  # Use # for headings
            "bullets": "-",          # Use - for bullet points
            "strip": ["script", "style"]  # Remove script and style tags
        }

        # 递归限制管理
        self.original_recursion_limit = sys.getrecursionlimit()

        logger.debug("MarkdownConverter initialized",
                    recursion_limit=self.markdown_config.recursion_limit,
                    max_html_depth=self.markdown_config.max_html_depth,
                    preprocessing_enabled=self.markdown_config.enable_preprocessing)
    
    def convert_to_markdown(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Convert HTML content to Markdown with fallback strategies

        Args:
            html_content: Cleaned HTML content
            url: Source URL for context

        Returns:
            Markdown content with metadata
        """
        logger.info("Starting Markdown conversion with fallback strategies", url=url)

        # 尝试各种策略
        for strategy_index, strategy in enumerate(self.markdown_config.fallback_strategies):
            try:
                if strategy_index == 0:
                    # 第一次尝试：正常转换
                    result = self._convert_with_increased_limit(html_content, url)
                elif strategy == "preprocessing":
                    # 第二次尝试：预处理后转换
                    logger.info("Trying conversion with HTML preprocessing", url=url)
                    preprocessed_html = self._preprocess_html(html_content)
                    result = self._convert_with_increased_limit(preprocessed_html, url)
                elif strategy == "text_extraction":
                    # 第三次尝试：仅提取文本
                    logger.info("Trying text extraction fallback", url=url)
                    result = self._extract_text_content(html_content, url)
                else:
                    continue

                if result["success"]:
                    if strategy_index > 0:
                        result["fallback_strategy"] = strategy
                        logger.info("Markdown conversion succeeded with fallback",
                                  url=url, strategy=strategy)
                    return result

            except RecursionError as e:
                logger.warning(f"Recursion error in strategy '{strategy}', trying next strategy",
                             url=url, strategy=strategy, error=str(e))
                continue
            except Exception as e:
                logger.warning(f"Error in strategy '{strategy}', trying next strategy",
                             url=url, strategy=strategy, error=str(e))
                continue

        # 所有策略都失败了
        logger.error("All Markdown conversion strategies failed", url=url)
        return {
            "url": url,
            "markdown_content": "",
            "success": False,
            "error": "All conversion strategies failed",
            "strategies_attempted": self.markdown_config.fallback_strategies
        }
    
    def _clean_markdown(self, markdown: str) -> str:
        """
        Clean up markdown content
        
        Args:
            markdown: Raw markdown content
            
        Returns:
            Cleaned markdown content
        """
        # Remove excessive blank lines
        lines = markdown.split('\n')
        cleaned_lines = []
        prev_blank = False
        
        for line in lines:
            is_blank = line.strip() == ""
            if is_blank and prev_blank:
                continue  # Skip consecutive blank lines
            cleaned_lines.append(line)
            prev_blank = is_blank
        
        return '\n'.join(cleaned_lines).strip()

    def _convert_with_increased_limit(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Convert HTML to Markdown with increased recursion limit

        Args:
            html_content: HTML content to convert
            url: Source URL for context

        Returns:
            Conversion result
        """
        original_limit = sys.getrecursionlimit()

        try:
            # 临时增加递归限制
            sys.setrecursionlimit(self.markdown_config.recursion_limit)

            # Convert HTML to Markdown
            markdown_content = md(html_content, **self.conversion_options)

            # Clean up the markdown
            cleaned_markdown = self._clean_markdown(markdown_content)

            # Calculate metrics
            line_count = len(cleaned_markdown.split('\n'))
            word_count = len(cleaned_markdown.split())

            logger.debug("Markdown conversion completed successfully",
                        url=url,
                        line_count=line_count,
                        word_count=word_count,
                        recursion_limit=self.markdown_config.recursion_limit)

            return {
                "url": url,
                "markdown_content": cleaned_markdown,
                "line_count": line_count,
                "word_count": word_count,
                "success": True
            }

        finally:
            # 恢复原始递归限制
            sys.setrecursionlimit(original_limit)

    def _preprocess_html(self, html_content: str) -> str:
        """
        Preprocess deeply nested HTML to reduce complexity

        Args:
            html_content: Original HTML content

        Returns:
            Preprocessed HTML content
        """
        if not self.markdown_config.enable_preprocessing:
            return html_content

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            max_depth = self.markdown_config.max_html_depth

            # 移除过度嵌套的div
            for div in soup.find_all('div'):
                depth = len(list(div.parents))
                if depth > max_depth:
                    # 将深度嵌套的div内容提取出来
                    if div.string:
                        div.replace_with(div.string)
                    elif div.get_text(strip=True):
                        # 保留文本内容，但移除嵌套结构
                        text_content = div.get_text(separator=' ', strip=True)
                        div.replace_with(text_content)
                    else:
                        div.decompose()

            # 简化过度嵌套的列表
            for ul in soup.find_all(['ul', 'ol']):
                depth = len([p for p in ul.parents if p.name in ['ul', 'ol']])
                if depth > 5:  # 限制列表嵌套深度
                    ul.unwrap()

            # 移除空的嵌套标签
            for tag in soup.find_all():
                if not tag.get_text(strip=True) and not tag.find_all(['img', 'br', 'hr']):
                    tag.decompose()

            preprocessed_html = str(soup)

            logger.debug("HTML preprocessing completed",
                        original_length=len(html_content),
                        preprocessed_length=len(preprocessed_html),
                        max_depth=max_depth)

            return preprocessed_html

        except Exception as e:
            logger.warning("HTML preprocessing failed, using original content", error=str(e))
            return html_content

    def _extract_text_content(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Extract text content as fallback when Markdown conversion fails

        Args:
            html_content: HTML content to extract text from
            url: Source URL for context

        Returns:
            Text extraction result
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 提取文本内容，保持基本结构
            text_content = soup.get_text(separator='\n\n', strip=True)

            # 基本清理
            lines = text_content.split('\n')
            cleaned_lines = []
            prev_blank = False

            for line in lines:
                line = line.strip()
                is_blank = line == ""

                if is_blank and prev_blank:
                    continue  # Skip consecutive blank lines

                cleaned_lines.append(line)
                prev_blank = is_blank

            cleaned_text = '\n'.join(cleaned_lines)

            # Calculate metrics
            line_count = len(cleaned_lines)
            word_count = len(cleaned_text.split())

            logger.info("Text extraction completed as fallback",
                       url=url,
                       line_count=line_count,
                       word_count=word_count)

            return {
                "url": url,
                "markdown_content": cleaned_text,
                "line_count": line_count,
                "word_count": word_count,
                "success": True,
                "conversion_method": "text_extraction"
            }

        except Exception as e:
            logger.error("Text extraction failed", url=url, error=str(e))
            return {
                "url": url,
                "markdown_content": "",
                "success": False,
                "error": f"Text extraction failed: {str(e)}"
            }


class ContentQualityAssessor:
    """内容质量评估器"""

    def __init__(self):
        """Initialize content quality assessor"""
        self.min_word_count = 100
        self.min_content_length = 500

    def assess_quality(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess content quality

        Args:
            content_data: Content data to assess

        Returns:
            Quality assessment results
        """
        try:
            url = content_data.get("url", "")
            word_count = content_data.get("word_count", 0)
            content_length = content_data.get("content_length", 0)
            title = content_data.get("title", "")

            # Calculate quality score
            quality_score = 0
            quality_issues = []

            # Check word count
            if word_count >= self.min_word_count:
                quality_score += 30
            else:
                quality_issues.append(f"Low word count: {word_count} < {self.min_word_count}")

            # Check content length
            if content_length >= self.min_content_length:
                quality_score += 30
            else:
                quality_issues.append(f"Short content: {content_length} < {self.min_content_length}")

            # Check title presence (with None safety)
            if title and isinstance(title, str) and len(title.strip()) > 0:
                quality_score += 20
            else:
                quality_issues.append("Missing or empty title")

            # Check content structure
            text_content = content_data.get("text_content", "")
            if text_content and len(text_content.split('\n')) > 3:
                quality_score += 20
            else:
                quality_issues.append("Poor content structure")

            # Determine quality level
            if quality_score >= 80:
                quality_level = "high"
            elif quality_score >= 60:
                quality_level = "medium"
            else:
                quality_level = "low"

            logger.info("Content quality assessment completed",
                       url=url,
                       quality_score=quality_score,
                       quality_level=quality_level,
                       issues_count=len(quality_issues))

            return {
                "url": url,
                "quality_score": quality_score,
                "quality_level": quality_level,
                "quality_issues": quality_issues,
                "word_count": word_count,
                "content_length": content_length,
                "has_title": bool(title and title.strip()),
                "success": True
            }

        except Exception as e:
            logger.error("Content quality assessment failed",
                        url=content_data.get("url", ""),
                        error=str(e))
            return {
                "url": content_data.get("url", ""),
                "quality_score": 0,
                "quality_level": "unknown",
                "quality_issues": [f"Assessment failed: {str(e)}"],
                "success": False,
                "error": str(e)
            }


class WebContentProcessor:
    """
    完整的网页内容处理器

    集成网页抓取、HTML清洁、Markdown转换和质量评估功能
    """

    def __init__(self, config: Optional[Config] = None):
        """
        Initialize web content processor

        Args:
            config: Configuration object
        """
        self.config = config or Config()

        # Initialize components
        # 使用现有的代理配置
        proxy_url = None
        if hasattr(self.config, 'proxy') and self.config.proxy:
            proxy_url = self.config.proxy.https_proxy or self.config.proxy.http_proxy

        # 获取网络配置
        verify_ssl = True
        retry_config = None
        if hasattr(self.config, 'network') and self.config.network:
            verify_ssl = self.config.network.verify_ssl
            retry_config = self.config.network.retry
        else:
            # 向后兼容
            verify_ssl = getattr(self.config, 'verify_ssl', True)

        self.scraper = WebScraper(proxy_config=proxy_url, verify_ssl=verify_ssl, retry_config=retry_config)
        self.html_cleaner = HTMLCleaner()
        self.markdown_converter = MarkdownConverter(config=self.config)
        self.quality_assessor = ContentQualityAssessor()
        self.cache_manager = URLCacheManager(config=self.config)
        self.file_manager = ContentFileManager(config=self.config)

        # Initialize stats
        self.stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "cached_hits": 0
        }

        logger.info("WebContentProcessor initialized")

    async def initialize(self):
        """Initialize the web content processor and its components"""
        # Initialize the cache manager
        await self.cache_manager.initialize()
        logger.info("WebContentProcessor initialization completed")

    async def process_url(self, url: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Process a single URL through the complete pipeline

        Args:
            url: URL to process
            use_cache: Whether to use cached results

        Returns:
            Complete processing results
        """
        try:
            logger.info("Starting URL processing", url=url, use_cache=use_cache)

            # Check cache first
            if use_cache:
                is_processed = await self.cache_manager.is_url_processed(url)
                if is_processed:
                    # Try to load cached result from metadata
                    url_metadata = await self.cache_manager.get_url_metadata(url)
                    if url_metadata and url_metadata.get("success", False):
                        logger.info("Using cached result", url=url)
                        # Return a simplified cached result
                        return {
                            "url": url,
                            "success": True,
                            "cached": True,
                            "metadata": url_metadata.get("metadata", {}),
                            "timestamp": url_metadata.get("timestamp", "")
                        }

            # Step 1: Fetch web content
            fetch_result = await self.scraper.fetch_url(url)
            if not fetch_result.get("success", False):
                return {
                    "url": url,
                    "success": False,
                    "error": fetch_result.get("error", "Fetch failed"),
                    "stage": "fetch"
                }

            # Step 2: Clean HTML content
            clean_result = self.html_cleaner.clean_html(
                fetch_result["content"],
                url
            )
            if not clean_result.get("success", False):
                return {
                    "url": url,
                    "success": False,
                    "error": clean_result.get("error", "HTML cleaning failed"),
                    "stage": "clean"
                }

            # Step 3: Convert to Markdown
            markdown_result = self.markdown_converter.convert_to_markdown(
                clean_result["content"],
                url
            )
            if not markdown_result.get("success", False):
                return {
                    "url": url,
                    "success": False,
                    "error": markdown_result.get("error", "Markdown conversion failed"),
                    "stage": "markdown"
                }

            # Step 4: Assess content quality
            quality_result = self.quality_assessor.assess_quality(clean_result)

            # Combine all results
            final_result = {
                "url": url,
                "success": True,
                "title": clean_result.get("title", ""),
                "content": {
                    "html": clean_result.get("content", ""),
                    "text": clean_result.get("text_content", ""),
                    "markdown": markdown_result.get("markdown_content", "")
                },
                "metadata": {
                    "fetch_time": fetch_result.get("fetch_time", 0),
                    "content_size": fetch_result.get("content_size", 0),
                    "word_count": clean_result.get("word_count", 0),
                    "content_length": clean_result.get("content_length", 0),
                    "line_count": markdown_result.get("line_count", 0),
                    "quality_score": quality_result.get("quality_score", 0),
                    "quality_level": quality_result.get("quality_level", "unknown"),
                    "quality_issues": quality_result.get("quality_issues", [])
                },
                "timestamp": datetime.now().isoformat()
            }

            # Save content to files
            raw_content_path = None
            markdown_path = None

            try:
                # Generate file paths
                file_paths = self.cache_manager.generate_file_paths(url, "html")
                raw_content_path = file_paths["raw_content_path"]
                markdown_path = file_paths["markdown_path"]

                # Save raw HTML content
                raw_saved = await self.file_manager.save_raw_content(
                    Path(raw_content_path),
                    fetch_result["content"]
                )

                # Save markdown content
                markdown_saved = await self.file_manager.save_markdown_content(
                    Path(markdown_path),
                    markdown_result["markdown_content"]
                )

                if not raw_saved:
                    logger.warning("Failed to save raw content", url=url, path=raw_content_path)
                    raw_content_path = None

                if not markdown_saved:
                    logger.warning("Failed to save markdown content", url=url, path=markdown_path)
                    markdown_path = None

                logger.debug("Content files saved",
                           url=url,
                           raw_saved=raw_saved,
                           markdown_saved=markdown_saved)

            except Exception as e:
                logger.error("Failed to save content files", url=url, error=str(e))
                raw_content_path = None
                markdown_path = None

            # Cache the result with file paths
            if use_cache:
                await self.cache_manager.mark_url_processed(
                    url=url,
                    success=True,
                    raw_content_path=raw_content_path,
                    markdown_path=markdown_path,
                    metadata=final_result["metadata"]
                )

            logger.info("URL processing completed successfully",
                       url=url,
                       quality_level=final_result["metadata"]["quality_level"],
                       word_count=final_result["metadata"]["word_count"])

            return final_result

        except Exception as e:
            logger.error("URL processing failed", url=url, error=str(e))
            return {
                "url": url,
                "success": False,
                "error": f"Processing failed: {str(e)}",
                "stage": "processing"
            }

    async def process_multiple_urls(self, urls: List[str], use_cache: bool = True,
                                  max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """
        Process multiple URLs concurrently

        Args:
            urls: List of URLs to process
            use_cache: Whether to use cached results
            max_concurrent: Maximum concurrent processing tasks

        Returns:
            List of processing results
        """
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.process_url(url, use_cache)

        logger.info("Starting batch URL processing",
                   total_urls=len(urls),
                   max_concurrent=max_concurrent,
                   use_cache=use_cache)

        tasks = [process_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error("Processing task failed", url=urls[i], error=str(result))
                processed_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": f"Task failed: {str(result)}",
                    "stage": "task_error"
                })
            else:
                processed_results.append(result)

        successful_count = sum(1 for r in processed_results if r.get("success", False))
        logger.info("Batch URL processing completed",
                   total_urls=len(urls),
                   successful_count=successful_count,
                   failed_count=len(urls) - successful_count)

        return processed_results

    async def process_urls(self, urls: List[str], max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """
        Process multiple URLs (alias for process_multiple_urls for backward compatibility)

        Args:
            urls: List of URLs to process
            max_concurrent: Maximum concurrent processing tasks

        Returns:
            List of processing results
        """
        return await self.process_multiple_urls(urls, use_cache=True, max_concurrent=max_concurrent)

    async def save_results(self, results: List[Dict[str, Any]],
                          output_dir: str = "output") -> Dict[str, Any]:
        """
        Save processing results to files

        Args:
            results: Processing results to save
            output_dir: Output directory

        Returns:
            Save operation results
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save summary JSON
            summary_file = output_path / f"processing_summary_{timestamp}.json"
            summary_data = {
                "timestamp": datetime.now().isoformat(),
                "total_urls": len(results),
                "successful_count": sum(1 for r in results if r.get("success", False)),
                "failed_count": sum(1 for r in results if not r.get("success", False)),
                "results": results
            }

            async with aiofiles.open(summary_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(summary_data, indent=2, ensure_ascii=False))

            # Save individual markdown files for successful results
            markdown_files = []
            for result in results:
                if result.get("success", False) and result.get("content", {}).get("markdown"):
                    # Create safe filename from URL
                    url = result["url"]
                    domain = urlparse(url).netloc.replace(".", "_")
                    title = result.get("title") or "untitled"
                    safe_title = "".join(c for c in title[:50]
                                       if c.isalnum() or c in (' ', '-', '_')).strip()
                    safe_title = safe_title.replace(" ", "_")

                    markdown_filename = f"{domain}_{safe_title}_{timestamp}.md"
                    markdown_file = output_path / markdown_filename

                    # Create markdown content with metadata
                    title_for_markdown = result.get('title') or 'Untitled'
                    markdown_content = f"""# {title_for_markdown}

**Source URL:** {url}
**Processing Time:** {result.get('timestamp', '')}
**Quality Level:** {result.get('metadata', {}).get('quality_level', 'unknown')}
**Word Count:** {result.get('metadata', {}).get('word_count', 0)}

---

{result.get('content', {}).get('markdown', '')}
"""

                    async with aiofiles.open(markdown_file, 'w', encoding='utf-8') as f:
                        await f.write(markdown_content)

                    markdown_files.append(str(markdown_file))

            logger.info("Results saved successfully",
                       output_dir=output_dir,
                       summary_file=str(summary_file),
                       markdown_files_count=len(markdown_files))

            return {
                "success": True,
                "output_dir": str(output_path),
                "summary_file": str(summary_file),
                "markdown_files": markdown_files,
                "total_files": len(markdown_files) + 1
            }

        except Exception as e:
            logger.error("Failed to save results", error=str(e))
            return {
                "success": False,
                "error": str(e)
            }

    async def close(self):
        """Close all resources"""
        await self.scraper.close()
        logger.info("WebContentProcessor closed")

    async def cleanup(self):
        """Cleanup resources (alias for close method for backward compatibility)"""
        await self.close()


# Convenience functions for easy usage
async def process_single_url(url: str, config: Optional[Config] = None) -> Dict[str, Any]:
    """
    Process a single URL (convenience function)

    Args:
        url: URL to process
        config: Configuration object

    Returns:
        Processing result
    """
    processor = WebContentProcessor(config)
    try:
        result = await processor.process_url(url)
        return result
    finally:
        await processor.close()


async def process_urls_batch(urls: List[str], config: Optional[Config] = None,
                           max_concurrent: int = 3) -> List[Dict[str, Any]]:
    """
    Process multiple URLs (convenience function)

    Args:
        urls: List of URLs to process
        config: Configuration object
        max_concurrent: Maximum concurrent processing tasks

    Returns:
        List of processing results
    """
    processor = WebContentProcessor(config)
    try:
        results = await processor.process_multiple_urls(urls, max_concurrent=max_concurrent)
        return results
    finally:
        await processor.close()
